import pyautogui
import time
from pywinauto import Application, findwindows
from pywinauto.controls.uiawrapper import UIAWrapper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CachemanAutomator:
    def __init__(self):
        self.app = None
        self.main_window = None
        
    def connect_to_cacheman(self):
        """连接到Cacheman应用程序"""
        try:
            # 查找所有Cacheman相关窗口
            windows = findwindows.find_windows(title_re=".*Cacheman.*")
            if not windows:
                logger.error("未找到Cacheman窗口，请确保软件已启动")
                return False

            logger.info(f"找到{len(windows)}个Cacheman相关窗口")

            # 打印所有窗口信息以便调试
            import win32gui
            target_handle = None

            for i, handle in enumerate(windows):
                try:
                    title = win32gui.GetWindowText(handle)
                    class_name = win32gui.GetClassName(handle)
                    is_visible = win32gui.IsWindowVisible(handle)
                    rect = win32gui.GetWindowRect(handle)
                    area = (rect[2] - rect[0]) * (rect[3] - rect[1])

                    logger.info(f"窗口{i+1}: 标题='{title}', 类名='{class_name}', 可见={is_visible}, 面积={area}")

                    # 选择主窗口的条件：
                    # 1. 不包含"文件资源管理器"
                    # 2. 可见
                    # 3. 面积较大
                    if (is_visible and
                        "文件资源管理器" not in title and
                        "File Explorer" not in title and
                        area > 10000):  # 面积阈值
                        if not target_handle or area > win32gui.GetWindowRect(target_handle)[2] * win32gui.GetWindowRect(target_handle)[3]:
                            target_handle = handle

                except Exception as e:
                    logger.warning(f"获取窗口{i+1}信息失败: {e}")
                    continue

            # 如果没找到合适的窗口，使用第一个可见窗口
            if not target_handle:
                for handle in windows:
                    if win32gui.IsWindowVisible(handle):
                        title = win32gui.GetWindowText(handle)
                        if "文件资源管理器" not in title and "File Explorer" not in title:
                            target_handle = handle
                            break

            if not target_handle:
                logger.error("未找到合适的Cacheman主窗口")
                return False

            # 连接到应用程序
            self.app = Application().connect(handle=target_handle)
            self.main_window = self.app.window(handle=target_handle)

            # 验证窗口是否正确
            window_title = self.main_window.window_text()
            logger.info(f"选择的目标窗口: {window_title}")

            # 确保窗口在前台
            self.main_window.set_focus()
            time.sleep(0.5)

            return True

        except Exception as e:
            logger.error(f"连接Cacheman失败: {e}")
            return False

    def verify_window_active(self):
        """验证目标窗口是否仍然活跃"""
        try:
            if self.main_window and self.main_window.exists():
                return True
            else:
                logger.warning("目标窗口已关闭或不存在")
                return False
        except Exception as e:
            logger.error(f"验证窗口状态失败: {e}")
            return False
    
    def wait_and_click(self, control, timeout=5):
        """等待控件可用并点击"""
        try:
            # 确保主窗口仍然活跃
            if not self.verify_window_active():
                return False

            control.wait('ready', timeout=timeout)

            # 确保窗口在前台
            self.main_window.set_focus()
            time.sleep(0.2)

            control.click()
            time.sleep(0.5)  # 短暂等待界面响应
            logger.info(f"成功点击控件: {control}")
            return True
        except Exception as e:
            logger.error(f"点击控件失败: {e}")
            return False

    def find_control_with_fallback(self, primary_title, fallback_titles=None, control_type="Button"):
        """使用多种方式查找控件，包括内部弹出窗口"""
        if fallback_titles is None:
            fallback_titles = []

        logger.info(f"查找控件: {primary_title}, 类型: {control_type}")

        # 首先打印所有可用的控件以便调试
        try:
            logger.info("当前窗口的所有控件:")
            self.main_window.print_control_identifiers()
        except Exception as e:
            logger.warning(f"无法打印控件列表: {e}")

        # 查找内部弹出窗口的函数
        def find_internal_dialogs(window, depth=0):
            """查找内部弹出的对话框或子窗口"""
            internal_dialogs = []
            if depth > 3:  # 限制递归深度
                return internal_dialogs

            try:
                children = window.children()
                for child in children:
                    try:
                        # 检查是否是对话框类型的控件
                        class_name = child.class_name()
                        window_text = child.window_text()

                        # 常见的内部对话框类名
                        dialog_classes = [
                            'dialog', 'popup', 'form', 'panel', 'frame',
                            'window', 'container', 'group', 'box'
                        ]

                        if any(cls in class_name.lower() for cls in dialog_classes):
                            logger.info(f"找到可能的内部对话框: 类名='{class_name}', 文本='{window_text}'")
                            internal_dialogs.append(child)

                        # 递归查找更深层的对话框
                        internal_dialogs.extend(find_internal_dialogs(child, depth + 1))

                    except Exception as e:
                        logger.debug(f"检查子控件失败: {e}")
                        continue
            except Exception as e:
                logger.debug(f"获取子控件失败: {e}")

            return internal_dialogs

        # 递归查找所有子控件
        def find_control_recursive(window, title, control_type=None, depth=0):
            """递归查找控件"""
            if depth > 5:  # 限制递归深度
                return None

            try:
                # 在当前窗口查找
                if control_type:
                    control = window.child_window(title=title, control_type=control_type)
                else:
                    control = window.child_window(title=title)

                if control.exists():
                    return control
            except:
                pass

            # 在子窗口中递归查找
            try:
                children = window.children()
                for child in children:
                    result = find_control_recursive(child, title, control_type, depth + 1)
                    if result:
                        return result
            except:
                pass

            return None

        # 首先查找内部弹出窗口
        internal_dialogs = find_internal_dialogs(self.main_window)
        logger.info(f"找到 {len(internal_dialogs)} 个可能的内部对话框")

        # 在内部对话框中查找控件
        for dialog in internal_dialogs:
            try:
                logger.info(f"在内部对话框中查找控件: {primary_title}")
                dialog.print_control_identifiers()

                # 在对话框中查找主要标题
                if control_type:
                    control = dialog.child_window(title=primary_title, control_type=control_type)
                else:
                    control = dialog.child_window(title=primary_title)

                if control.exists():
                    logger.info(f"在内部对话框中找到控件: {primary_title}")
                    return control

                # 在对话框中查找备选标题
                for title in fallback_titles:
                    try:
                        if ".*" in title:  # 正则表达式
                            control = dialog.child_window(title_re=title, control_type=control_type)
                        else:
                            control = dialog.child_window(title=title, control_type=control_type)
                        if control.exists():
                            logger.info(f"在内部对话框中找到控件(备选): {title}")
                            return control
                    except Exception as e:
                        logger.debug(f"在内部对话框中查找备选标题 '{title}' 失败: {e}")
                        continue

            except Exception as e:
                logger.debug(f"在内部对话框中查找控件失败: {e}")
                continue

        # 尝试主要标题
        try:
            control = self.main_window.child_window(title=primary_title, control_type=control_type)
            if control.exists():
                logger.info(f"找到控件: {primary_title}")
                return control
        except Exception as e:
            logger.debug(f"主标题查找失败: {e}")

        # 尝试递归查找主要标题
        try:
            control = find_control_recursive(self.main_window, primary_title, control_type)
            if control:
                logger.info(f"递归找到控件: {primary_title}")
                return control
        except Exception as e:
            logger.debug(f"递归查找主标题失败: {e}")

        # 尝试备选标题
        for title in fallback_titles:
            try:
                if ".*" in title:  # 正则表达式
                    control = self.main_window.child_window(title_re=title, control_type=control_type)
                else:
                    control = self.main_window.child_window(title=title, control_type=control_type)
                if control.exists():
                    logger.info(f"找到控件(备选): {title}")
                    return control
            except Exception as e:
                logger.debug(f"备选标题 '{title}' 查找失败: {e}")
                continue

            # 尝试递归查找备选标题
            try:
                control = find_control_recursive(self.main_window, title, control_type)
                if control:
                    logger.info(f"递归找到控件(备选): {title}")
                    return control
            except Exception as e:
                logger.debug(f"递归查找备选标题 '{title}' 失败: {e}")
                continue

        # 尝试不指定控件类型
        try:
            control = self.main_window.child_window(title=primary_title)
            if control.exists():
                logger.info(f"找到控件(无类型限制): {primary_title}")
                return control
        except:
            pass

        # 尝试递归查找不指定控件类型
        try:
            control = find_control_recursive(self.main_window, primary_title, None)
            if control:
                logger.info(f"递归找到控件(无类型限制): {primary_title}")
                return control
        except:
            pass

        logger.warning(f"未找到控件: {primary_title}")
        return None

    def find_and_handle_internal_popup(self, button_titles, action_name="操作"):
        """专门处理内部弹出窗口中的按钮点击"""
        logger.info(f"查找内部弹出窗口中的按钮: {button_titles}")

        try:
            # 等待一下让弹窗完全加载
            time.sleep(0.5)

            # 获取当前窗口的所有子控件
            def scan_for_buttons(parent, depth=0):
                """扫描所有可能包含按钮的控件"""
                if depth > 4:
                    return None

                try:
                    children = parent.children()
                    for child in children:
                        try:
                            # 检查当前控件是否是我们要找的按钮
                            for title in button_titles:
                                try:
                                    if ".*" in title:
                                        button = child.child_window(title_re=title, control_type="Button")
                                    else:
                                        button = child.child_window(title=title, control_type="Button")

                                    if button.exists():
                                        logger.info(f"在内部弹窗中找到按钮: {title}")
                                        return button
                                except:
                                    continue

                            # 递归搜索子控件
                            result = scan_for_buttons(child, depth + 1)
                            if result:
                                return result

                        except Exception as e:
                            logger.debug(f"扫描子控件失败: {e}")
                            continue

                except Exception as e:
                    logger.debug(f"获取子控件失败: {e}")

                return None

            # 扫描主窗口
            button = scan_for_buttons(self.main_window)
            if button:
                logger.info(f"准备点击内部弹窗按钮执行{action_name}")
                return self.wait_and_click(button)
            else:
                logger.warning(f"未在内部弹窗中找到按钮: {button_titles}")
                return False

        except Exception as e:
            logger.error(f"处理内部弹窗失败: {e}")
            return False

    def find_popup_dialog(self):
        """专门查找弹出的对话框窗口"""
        try:
            # 查找所有子窗口，寻找弹出的对话框
            def find_dialog_recursive(parent, depth=0):
                if depth > 3:
                    return None

                try:
                    children = parent.children()
                    for child in children:
                        try:
                            class_name = child.class_name()
                            window_text = child.window_text()

                            # 检查是否是弹出对话框的特征
                            # 通常弹出对话框会有特定的类名或包含特定文本
                            dialog_indicators = [
                                "自动优化", "Auto Optimize", "配置", "Configuration",
                                "设置", "Settings", "优化设置", "Optimization"
                            ]

                            if (any(indicator in window_text for indicator in dialog_indicators) or
                                any(keyword in class_name.lower() for keyword in ['dialog', 'form', 'popup'])):
                                logger.info(f"找到弹出对话框: 类名='{class_name}', 文本='{window_text}'")
                                return child

                            # 递归查找
                            result = find_dialog_recursive(child, depth + 1)
                            if result:
                                return result

                        except Exception as e:
                            logger.debug(f"检查子控件失败: {e}")
                            continue
                except Exception as e:
                    logger.debug(f"获取子控件失败: {e}")

                return None

            return find_dialog_recursive(self.main_window)

        except Exception as e:
            logger.error(f"查找弹出对话框失败: {e}")
            return None

    def find_separate_popup_window(self):
        """查找独立的弹出窗口（不是内部对话框）"""
        try:
            # 查找所有应用程序的窗口
            all_windows = self.app.windows()
            logger.info(f"应用程序共有 {len(all_windows)} 个窗口")

            for i, window in enumerate(all_windows):
                try:
                    title = window.window_text()
                    class_name = window.class_name()
                    is_visible = window.is_visible()

                    logger.info(f"窗口{i+1}: 标题='{title}', 类名='{class_name}', 可见={is_visible}")

                    # 查找自动优化弹出窗口的特征
                    if (is_visible and
                        ("自动优化" in title or "Auto Optimize" in title or
                         title == "自动优化" or "优化" in title)):
                        logger.info(f"找到自动优化弹出窗口: {title}")
                        return window

                except Exception as e:
                    logger.debug(f"检查窗口{i+1}失败: {e}")
                    continue

            return None

        except Exception as e:
            logger.error(f"查找独立弹出窗口失败: {e}")
            return None

    def step1_click_cancel(self):
        """步骤1: 点击取消按钮（在独立弹出窗口中）"""
        logger.info("执行步骤1: 点击取消按钮")
        try:
            # 查找独立的弹出窗口
            popup_window = self.find_separate_popup_window()
            if popup_window:
                logger.info("找到独立弹出窗口，在其中查找取消按钮")
                try:
                    # 使用正确的方法打印控件信息
                    try:
                        popup_window.print_control_identifiers()
                    except AttributeError:
                        # 如果没有这个方法，尝试其他方式获取控件信息
                        logger.info("尝试获取弹出窗口的控件信息...")
                        children = popup_window.children()
                        logger.info(f"弹出窗口有 {len(children)} 个子控件")
                        for i, child in enumerate(children[:10]):  # 只显示前10个
                            try:
                                logger.info(f"子控件{i+1}: {child.window_text()} - {child.class_name()}")
                            except:
                                logger.info(f"子控件{i+1}: 无法获取信息")

                    # 在弹出窗口中查找取消按钮
                    cancel_titles = ["取消", "Cancel", "关闭", "Close"]
                    for title in cancel_titles:
                        try:
                            cancel_button = popup_window.child_window(title=title, control_type="Button")
                            if cancel_button.exists():
                                logger.info(f"在弹出窗口中找到取消按钮: {title}")
                                cancel_button.click()
                                time.sleep(1)
                                return True
                        except Exception as e:
                            logger.debug(f"查找取消按钮 '{title}' 失败: {e}")
                            continue

                    # 如果没找到按钮，尝试通过不同方式查找
                    try:
                        # 尝试查找所有子控件，不限制控件类型
                        all_children = popup_window.children()
                        logger.info(f"弹出窗口中总共有 {len(all_children)} 个子控件")

                        buttons = []
                        for child in all_children:
                            try:
                                class_name = child.class_name()
                                window_text = child.window_text()
                                # 查找可能是按钮的控件
                                if ("Button" in class_name or "Btn" in class_name or
                                    window_text in ["取消", "Cancel", "关闭", "Close", "优化", "Optimize"]):
                                    buttons.append(child)
                                    logger.info(f"找到可能的按钮: '{window_text}' - {class_name}")
                            except:
                                continue

                        logger.info(f"找到 {len(buttons)} 个可能的按钮控件")

                        # 尝试点击取消相关的按钮
                        for button in buttons:
                            try:
                                button_text = button.window_text()
                                logger.info(f"检查按钮: '{button_text}'")
                                if button_text in ["取消", "Cancel", "关闭", "Close"]:
                                    logger.info(f"尝试点击取消按钮: '{button_text}'")
                                    button.click()
                                    time.sleep(1)
                                    return True
                            except Exception as e:
                                logger.debug(f"点击按钮失败: {e}")

                        # 如果还是没找到，尝试通过坐标点击（根据截图，取消按钮在右下角）
                        try:
                            rect = popup_window.rectangle()
                            # 取消按钮大概在窗口右下角
                            cancel_x = rect.right - 60  # 距离右边60像素
                            cancel_y = rect.bottom - 30  # 距离底部30像素
                            logger.info(f"尝试通过坐标点击取消按钮: ({cancel_x}, {cancel_y})")
                            popup_window.click_input(coords=(cancel_x - rect.left, cancel_y - rect.top))
                            time.sleep(1)
                            return True
                        except Exception as e:
                            logger.debug(f"坐标点击失败: {e}")

                    except Exception as e:
                        logger.debug(f"查找按钮失败: {e}")

                except Exception as e:
                    logger.error(f"在弹出窗口中操作失败: {e}")

            # 如果没找到弹出窗口，可能没有弹窗，这是正常情况
            logger.info("未找到弹出窗口，可能没有需要取消的弹窗")
            return True

        except Exception as e:
            logger.error(f"步骤1失败: {e}")
            return False
    
    def find_button_in_main_window(self, button_titles):
        """在主窗口中查找按钮（包括图片按钮和工具栏按钮）"""
        logger.info("在主窗口中查找按钮")

        # 首先显示主窗口的所有控件信息
        try:
            logger.info("=== 主窗口控件结构 ===")
            self.main_window.print_control_identifiers()
        except Exception as e:
            logger.warning(f"无法打印主窗口控件: {e}")

        # 1. 直接在主窗口查找按钮
        for title in button_titles:
            try:
                if ".*" in title:  # 正则表达式
                    button = self.main_window.child_window(title_re=title, control_type="Button")
                else:
                    button = self.main_window.child_window(title=title, control_type="Button")

                if button.exists():
                    logger.info(f"在主窗口中找到按钮: '{title}'")
                    return button
            except Exception as e:
                logger.debug(f"查找按钮 '{title}' 失败: {e}")
                continue

        # 2. 查找所有类型的可点击控件（不仅仅是Button）
        clickable_types = ["Button", "ToolbarButton", "MenuItem", "Hyperlink", "Image", "Pane"]
        for control_type in clickable_types:
            try:
                controls = self.main_window.children(control_type=control_type)
                logger.info(f"找到 {len(controls)} 个 {control_type} 控件")

                for i, control in enumerate(controls):
                    try:
                        control_text = control.window_text()
                        control_class = control.class_name()
                        logger.info(f"{control_type}{i+1}: 文本='{control_text}', 类名='{control_class}'")

                        # 检查是否匹配任何目标标题
                        for target_title in button_titles:
                            if ".*" in target_title:
                                import re
                                if re.search(target_title, control_text):
                                    logger.info(f"通过正则匹配找到控件: '{control_text}' 匹配 '{target_title}'")
                                    return control
                            else:
                                if (target_title in control_text or control_text in target_title or
                                    target_title in control_class or control_class in target_title):
                                    logger.info(f"通过文本匹配找到控件: '{control_text}' 匹配 '{target_title}'")
                                    return control
                    except Exception as e:
                        logger.debug(f"检查{control_type}{i+1}失败: {e}")
                        continue
            except Exception as e:
                logger.debug(f"查找{control_type}控件失败: {e}")
                continue

        # 3. 查找工具栏中的控件
        try:
            logger.info("=== 查找工具栏控件 ===")
            # 查找所有可能的工具栏
            toolbar_classes = ["ToolBar", "ReBar", "CoolBar", "CommandBar"]
            for toolbar_class in toolbar_classes:
                try:
                    toolbars = self.main_window.children(class_name_re=f".*{toolbar_class}.*")
                    for toolbar in toolbars:
                        logger.info(f"检查工具栏: {toolbar.class_name()}")
                        toolbar_controls = toolbar.children()
                        for control in toolbar_controls:
                            try:
                                control_text = control.window_text()
                                control_class = control.class_name()
                                logger.info(f"工具栏控件: 文本='{control_text}', 类名='{control_class}'")

                                for target_title in button_titles:
                                    if ".*" in target_title:
                                        import re
                                        if re.search(target_title, control_text):
                                            logger.info(f"在工具栏中找到匹配控件: '{control_text}'")
                                            return control
                                    else:
                                        if target_title in control_text or control_text in target_title:
                                            logger.info(f"在工具栏中找到匹配控件: '{control_text}'")
                                            return control
                            except:
                                continue
                except Exception as e:
                    logger.debug(f"查找{toolbar_class}失败: {e}")
                    continue
        except Exception as e:
            logger.debug(f"查找工具栏失败: {e}")

        return None

    def step2_click_release_memory(self):
        """步骤2: 点击释放内存按钮（在主窗口工具栏中）"""
        logger.info("执行步骤2: 点击释放内存按钮")
        try:
            # 定义要查找的按钮标题
            release_titles = [
                "释放内存", "Release Memory", ".*释放.*", ".*Release.*",
                "Free Memory", "清理内存", "Clean Memory", "优化", "Optimize",
                "清理", "Clean", "释放", "Release", "Free"
            ]

            # 使用新的查找方法
            release_button = self.find_button_in_main_window(release_titles)

            if release_button:
                logger.info("找到释放内存按钮")
                return self.wait_and_click(release_button)
            else:
                logger.error("未找到释放内存按钮")
                return False

        except Exception as e:
            logger.error(f"步骤2失败: {e}")
            return False
    
    def step3_click_auto_optimize(self):
        """步骤3: 点击自动优化按钮"""
        logger.info("执行步骤3: 点击自动优化按钮")
        try:
            # 查找自动优化按钮
            auto_optimize_button = self.main_window.child_window(title="自动优化", control_type="Button")
            if not auto_optimize_button.exists():
                # 尝试英文版本
                auto_optimize_button = self.main_window.child_window(title="Auto Optimize", control_type="Button")
            
            if auto_optimize_button.exists():
                return self.wait_and_click(auto_optimize_button)
            else:
                logger.error("未找到自动优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤3失败: {e}")
            return False
    
    def step4_select_max_performance(self):
        """步骤4: 选择最大性能选项（在独立弹出窗口中）"""
        logger.info("执行步骤4: 选择最大性能选项")
        try:
            time.sleep(1)  # 等待弹窗出现

            # 查找独立的弹出窗口
            popup_window = self.find_separate_popup_window()
            if popup_window:
                logger.info("在独立弹出窗口中查找最大性能选项")
                try:
                    # 使用正确的方法打印控件信息
                    try:
                        popup_window.print_control_identifiers()
                    except AttributeError:
                        logger.info("尝试获取弹出窗口的控件信息...")
                        children = popup_window.children()
                        logger.info(f"弹出窗口有 {len(children)} 个子控件")

                    # 在弹出窗口中查找最大性能选项
                    performance_options = ["最大性能", "Maximum Performance"]
                    for option in performance_options:
                        try:
                            radio_button = popup_window.child_window(title=option, control_type="RadioButton")
                            if radio_button.exists():
                                logger.info(f"在独立弹出窗口中找到最大性能选项: {option}")
                                return self.wait_and_click(radio_button)
                        except Exception as e:
                            logger.debug(f"查找最大性能选项 '{option}' 失败: {e}")
                            continue

                    # 如果没找到，尝试查找所有单选按钮
                    try:
                        radio_buttons = popup_window.children(control_type="RadioButton")
                        logger.info(f"弹出窗口中找到 {len(radio_buttons)} 个单选按钮")
                        for i, radio in enumerate(radio_buttons):
                            try:
                                radio_text = radio.window_text()
                                logger.info(f"单选按钮{i+1}: '{radio_text}'")
                                if "最大性能" in radio_text or "Maximum" in radio_text or "Performance" in radio_text:
                                    logger.info(f"尝试选择单选按钮: '{radio_text}'")
                                    return self.wait_and_click(radio)
                            except Exception as e:
                                logger.debug(f"检查单选按钮{i+1}失败: {e}")
                    except Exception as e:
                        logger.debug(f"查找所有单选按钮失败: {e}")

                except Exception as e:
                    logger.error(f"在独立弹出窗口中操作失败: {e}")

            # 如果没找到独立弹出窗口，使用原有方法
            max_performance_radio = self.main_window.child_window(title="最大性能", control_type="RadioButton")
            if not max_performance_radio.exists():
                max_performance_radio = self.main_window.child_window(title_re=".*最大性能.*", control_type="RadioButton")
            if not max_performance_radio.exists():
                max_performance_radio = self.main_window.child_window(title_re=".*Maximum.*Performance.*", control_type="RadioButton")

            if max_performance_radio.exists():
                return self.wait_and_click(max_performance_radio)
            else:
                logger.error("未找到最大性能选项")
                return False
        except Exception as e:
            logger.error(f"步骤4失败: {e}")
            return False
    
    def step5_click_optimize(self):
        """步骤5: 点击优化按钮（在独立弹出窗口中）"""
        logger.info("执行步骤5: 点击优化按钮")
        try:
            # 查找独立的弹出窗口
            popup_window = self.find_separate_popup_window()
            if popup_window:
                logger.info("在独立弹出窗口中查找优化按钮")
                try:
                    # 使用正确的方法打印控件信息
                    try:
                        popup_window.print_control_identifiers()
                    except AttributeError:
                        logger.info("尝试获取弹出窗口的控件信息...")
                        children = popup_window.children()
                        logger.info(f"弹出窗口有 {len(children)} 个子控件")

                    # 在弹出窗口中查找优化按钮
                    optimize_options = ["优化", "Optimize"]
                    for option in optimize_options:
                        try:
                            optimize_button = popup_window.child_window(title=option, control_type="Button")
                            if optimize_button.exists():
                                logger.info(f"在独立弹出窗口中找到优化按钮: {option}")
                                return self.wait_and_click(optimize_button)
                        except Exception as e:
                            logger.debug(f"查找优化按钮 '{option}' 失败: {e}")
                            continue

                    # 如果没找到，尝试查找所有按钮
                    try:
                        buttons = popup_window.children(control_type="Button")
                        logger.info(f"弹出窗口中找到 {len(buttons)} 个按钮")
                        for i, button in enumerate(buttons):
                            try:
                                button_text = button.window_text()
                                logger.info(f"按钮{i+1}: '{button_text}'")
                                if "优化" in button_text or "Optimize" in button_text:
                                    logger.info(f"尝试点击优化按钮: '{button_text}'")
                                    return self.wait_and_click(button)
                            except Exception as e:
                                logger.debug(f"检查按钮{i+1}失败: {e}")
                    except Exception as e:
                        logger.debug(f"查找所有按钮失败: {e}")

                except Exception as e:
                    logger.error(f"在独立弹出窗口中操作失败: {e}")

            # 如果没找到独立弹出窗口，使用原有方法
            optimize_button = self.main_window.child_window(title="优化", control_type="Button")
            if not optimize_button.exists():
                optimize_button = self.main_window.child_window(title="Optimize", control_type="Button")

            if optimize_button.exists():
                return self.wait_and_click(optimize_button)
            else:
                logger.error("未找到优化按钮")
                return False
        except Exception as e:
            logger.error(f"步骤5失败: {e}")
            return False
    
    def execute_automation(self):
        """执行完整的自动化流程"""
        logger.info("开始执行Cacheman自动化操作")

        if not self.connect_to_cacheman():
            return False

        # 执行5个步骤
        steps = [
            ("点击取消按钮", self.step1_click_cancel),
            ("点击释放内存按钮", self.step2_click_release_memory),
            ("点击自动优化按钮", self.step3_click_auto_optimize),
            ("选择最大性能", self.step4_select_max_performance),
            ("点击优化按钮", self.step5_click_optimize)
        ]

        for i, (step_name, step_func) in enumerate(steps, 1):
            logger.info(f"准备执行步骤{i}: {step_name}")
            try:
                if not step_func():
                    logger.error(f"步骤{i}({step_name})执行失败")
                    # 对于步骤1（取消按钮），如果失败可能是因为没有弹窗，继续执行
                    if i == 1:
                        logger.info("步骤1失败可能是正常情况，继续执行后续步骤")
                        continue
                    else:
                        logger.error("停止自动化流程")
                        return False
                else:
                    logger.info(f"步骤{i}({step_name})执行成功")
            except Exception as e:
                logger.error(f"步骤{i}({step_name})执行异常: {e}")
                if i == 1:
                    logger.info("步骤1异常可能是正常情况，继续执行后续步骤")
                    continue
                else:
                    logger.error("停止自动化流程")
                    return False

            time.sleep(2)  # 步骤间等待

        logger.info("所有步骤执行完成")
        return True

def main():
    """主函数"""
    automator = CachemanAutomator()
    success = automator.execute_automation()
    
    if success:
        print("✅ Cacheman自动化操作完成")
    else:
        print("❌ Cacheman自动化操作失败")

if __name__ == "__main__":
    main()
